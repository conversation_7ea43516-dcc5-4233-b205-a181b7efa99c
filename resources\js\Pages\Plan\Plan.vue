<template>
    <AppLayout title="Pricing Plans">
        <div class="p-5 lg:p-16">
            <div class="w-full items-center justify-between mb-8">
                <h1 class="text-lg md:text-xl lg:text-3xl font-bold">
                    Pricing Plans
                </h1>
                <p class="text-sm md:text-md text-gray-500 mt-3">
                    Choose the plan that fits your needs.
                </p>
            </div>

            <!-- Pricing Plans -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Free Plan -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-2xl font-bold mb-4">Free</h2>
                    <p class="text-gray-600 mb-4">
                        Build AI Chatbot trained on your most basic website and
                        company data with limited support from Kimana Team.
                    </p>
                    <div class="flex items-baseline mb-4">
                        <span class="text-4xl font-bold">$0</span>
                        <span class="text-gray-500 ml-1">/mo</span>
                    </div>
                    <p class="text-gray-600 mb-4">Free Forever</p>
                    <button
                        class="w-full bg-gradient-to-r from-green-400 to-blue-500 text-white py-2 px-4 rounded hover:from-green-500 hover:to-blue-600 transition-all duration-300 mb-6"
                    >
                        Try it free
                    </button>
                    <h3 class="font-semibold mb-2">Free plan features:</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Create 1 bot
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Train bot with publicly available website links
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Train with 1 document
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Limited bot customization
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Access to 1 week of conversation history
                        </li>
                    </ul>
                </div>

                <!-- Pro Plan -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-2xl font-bold mb-4">Pro</h2>
                    <p class="text-gray-600 mb-4">
                        Build AI Chatbot trained on all your data with few
                        limits and dedicated Kimana Team Support.
                    </p>
                    <div class="flex items-baseline mb-4">
                        <span class="text-4xl font-bold">$5</span>
                        <span class="text-gray-500 ml-1">/mo</span>
                    </div>
                    <p class="text-gray-600 mb-4">Billed Annually</p>
                    <button
                        class="w-full bg-gradient-to-r from-green-400 to-blue-500 text-white py-2 px-4 rounded hover:from-green-500 hover:to-blue-600 transition-all duration-300 mb-6"
                    >
                        Try it free
                    </button>
                    <h3 class="font-semibold mb-2">Add more features :</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Create up to 8 bots
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Train with unlimited number of website links
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Train with 25 documents
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Unlimited bot customization
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Access to 3 months of conversations history
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Dedicated Kimana support contact
                        </li>
                    </ul>
                </div>

                <!-- Custom / Enterprise Plan -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-2xl font-bold mb-4">Custom / Enterprise</h2>
                    <p class="text-gray-600 mb-4">
                        Contact <NAME_EMAIL> for a custom solution
                        that will scale with your business.
                    </p>
                    <div class="mb-4 mt-5">
                        <span class="text-2xl font-bold"
                            >Contact for pricing</span
                        >
                    </div>
                    <button
                        class="w-full bg-gray-800 text-white py-2 px-4 rounded hover:bg-gray-700 transition-colors mb-6 mt-10"
                    >
                        Contact support
                    </button>
                    <h3 class="font-semibold mb-2">Scale with your org:</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Create Unlimited bots
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Train bots with unlimited website links
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Train bots with unlimited documents
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Unlimited bot customization
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Incorporate live chat with customers and team
                        </li>
                        <li class="flex items-center">
                            <icon
                                name="check"
                                class="w-5 h-5 text-green-500 mr-2"
                            ></icon>
                            Access to at least 1 year of conversation history
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script>
import AppLayout from "@/Layouts/AppLayout.vue";
import Icon from "@/Components/Global/Icon.vue";
import { Link } from "@inertiajs/vue3";

export default {
    components: {
        AppLayout,
        Icon,
        Link,
    },

    props: {
        currently_selected_chatbot: Object,
    },

    data() {
        return {
            // Add any reactive data here if needed
        };
    },

    methods: {
        // Add methods here if needed
    },
};
</script>
